# ==================== 导入模块 ====================
import pandas as pd
import numpy as np
import xgboost as xgb
from sklearn.model_selection import GridSearchCV, train_test_split, StratifiedKFold
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import (accuracy_score, balanced_accuracy_score,
                           precision_score, recall_score, f1_score,
                           confusion_matrix, roc_auc_score, average_precision_score)
from rdkit import Chem, RDLogger
from rdkit.Chem import AllChem, Descriptors
from rdkit.Avalon import pyAvalonTools
try:
    from mordred import Calculator, descriptors
except ImportError:
    print("错误: 缺少 'mordred' 库。")
    print("请在终端运行: pip install mordred")
    exit()

from tqdm import tqdm
import warnings
import os
import random
from sklearn.compose import ColumnTransformer
import joblib

# --- 基本配置 ---
lg = RDLogger.logger()
lg.setLevel(RDLogger.CRITICAL)
warnings.filterwarnings('ignore')

# ==================== 配置参数 ====================
CONFIG = {
    # 文件路径
    'raw_data_path': '../data/Rat_clean.xlsx',
    'output_dir': '../model/',  # 统一保存到model文件夹
    
    # 数据列名
    'smiles_column': 'Canonical smiles',
    'label_column': 'Result_new',

    # 数据划分
    'test_size': 0.2,
    'random_state': 42,
    
    # ===== 关键修改：在这里切换你要测试的特征类型 =====
    'feature_type': 'Avalon',  # 🔧 特征拼接：可选单一特征或组合特征

    # 🎯 可选的特征类型：
    # === 单一特征 ===
    # 'MACCS'     - 167维，药效团特征，速度快
    # 'Avalon'    - 2048维，结构特征，效果好
    # 'Mordred'   - 1613维，理化描述符，可解释性强

    # ===== 自动特征维度与类型配置 =====
    'feature_definitions': {
        'ECFP4':                {'type': 'fingerprint', 'size': 2048},
        'Avalon':               {'type': 'fingerprint', 'size': 2048},
        'MACCS':                {'type': 'fingerprint', 'size': 167},
        'PubChem':              {'type': 'fingerprint', 'size': 881},
        #'Mordred':              {'type': 'descriptor',  'size': 1613},
        'RDKit_Descriptors':    {'type': 'descriptor',  'size': 50},
        'Toxicity_Descriptors': {'type': 'descriptor',  'size': 15},
    },

    # 交叉验证
    'cv_folds': 10,
    
    # ===== XGBoost参数网格 (针对MACCS指纹167维优化) =====
    'param_grid': {
        'n_estimators': [500],
        'learning_rate': [0.02],
        'max_depth': [7],
        'min_child_weight': [1],
        'reg_lambda': [2],
        'reg_alpha': [1],
        'colsample_bytree': [0.8],
        'subsample': [0.8],
        'scale_pos_weight': [1],
        'gamma': [0.1]
    },
}

# ==================== 辅助函数 ====================
def set_random_seeds(seed=42):
    """完全固定所有随机种子，确保结果100%可重现"""
    random.seed(seed)
    np.random.seed(seed)
    os.environ['PYTHONHASHSEED'] = str(seed)
    try:
        import torch
        torch.manual_seed(seed)
        torch.cuda.manual_seed_all(seed)
        torch.backends.cudnn.deterministic = True
        torch.backends.cudnn.benchmark = False
    except ImportError: pass
    print(f"✅ 已设置所有随机种子为: {seed}")

def identify_outliers_and_hard_samples(df, smiles_col, label_col, method='auto'):
    """
    识别异常点和困难样本

    Args:
        df: 数据框
        smiles_col: SMILES列名
        label_col: 标签列名
        method: 检测方法 ('chemical', 'statistical', 'auto')

    Returns:
        outlier_indices: 异常点索引列表
    """
    outlier_indices = []

    print(f"\n🔍 开始异常点检测 (方法: {method})")

    if method in ['chemical', 'auto']:
        # 1. 化学结构异常检测
        print("  检测化学结构异常...")
        chem_outliers = []

        for i, smiles in enumerate(df[smiles_col]):
            try:
                mol = Chem.MolFromSmiles(smiles)
                if mol is None:
                    chem_outliers.append(i)
                    continue

                # 计算分子描述符
                mw = Descriptors.MolWt(mol)
                logp = Descriptors.MolLogP(mol)
                hbd = Descriptors.NumHDonors(mol)
                hba = Descriptors.NumHAcceptors(mol)

                # Lipinski规则 + 额外检查
                if (mw > 800 or mw < 50 or          # 分子量异常
                    logp > 8 or logp < -3 or        # LogP异常
                    hbd > 10 or hba > 15):          # 氢键异常
                    chem_outliers.append(i)

            except Exception:
                chem_outliers.append(i)

        outlier_indices.extend(chem_outliers)
        print(f"    发现 {len(chem_outliers)} 个化学结构异常样本")

    if method in ['statistical', 'auto']:
        # 2. 统计异常检测
        print("  检测统计异常...")

        # 基于标签分布的异常检测
        label_counts = df[label_col].value_counts()
        if len(label_counts) == 2:
            minority_class = label_counts.idxmin()
            minority_ratio = label_counts.min() / len(df)

            # 如果数据极度不平衡，可能需要特殊处理
            if minority_ratio < 0.05:
                print(f"    ⚠️ 数据不平衡严重: {minority_class}类仅占{minority_ratio:.1%}")

    # 去重
    outlier_indices = list(set(outlier_indices))

    print(f"✅ 异常点检测完成，共发现 {len(outlier_indices)} 个异常样本")
    print(f"   异常样本占比: {len(outlier_indices)/len(df):.1%}")

    return outlier_indices

def remove_outliers_interactive(df, outlier_indices, smiles_col, label_col):
    """
    交互式移除异常点
    """
    if not outlier_indices:
        print("✅ 未发现异常点，保持原数据")
        return df, []

    print(f"\n📊 异常点分析:")
    print(f"总样本数: {len(df)}")
    print(f"异常点数: {len(outlier_indices)}")
    print(f"异常点占比: {len(outlier_indices)/len(df):.1%}")

    # 显示异常点的标签分布
    outlier_labels = df.iloc[outlier_indices][label_col].value_counts()
    print(f"异常点标签分布: {outlier_labels.to_dict()}")

    # 自动决策：如果异常点太多(>10%)，只移除最明显的
    if len(outlier_indices) / len(df) > 0.1:
        print("⚠️ 异常点过多，建议保守处理")
        # 可以在这里添加更严格的筛选逻辑
        return df, []
    else:
        print("✅ 异常点数量合理，建议移除")
        clean_df = df.drop(index=outlier_indices).reset_index(drop=True)
        return clean_df, outlier_indices

def print_environment_info():
    """打印环境信息，帮助诊断不同服务器的差异"""
    import platform, sys, sklearn
    print("\n" + "="*50 + "\n🔍 环境信息诊断\n" + "="*50)
    print(f"Python版本: {sys.version.split(' ')[0]}")
    print(f"操作系统: {platform.system()} {platform.release()}")
    try:
        print(f"NumPy: {np.__version__}, Pandas: {pd.__version__}, XGBoost: {xgb.__version__}")
        print(f"Scikit-learn: {sklearn.__version__}, RDKit: {Chem.rdBase.rdkitVersion}")
    except Exception as e: print(f"无法获取库版本: {e}")
    print("="*50)

    # 添加数据一致性检查
    print(f"当前工作目录: {os.getcwd()}")
    print(f"Python可执行文件: {sys.executable}")

    # 检查关键库的具体版本
    try:
        print(f"NumPy随机状态: {np.random.get_state()[1][0]}")  # 显示随机状态
    except:
        pass

def get_rdkit_descriptors(mol):
    """计算精选的50个关键RDKit分子描述符 (针对毒性预测优化)"""
    descriptors = []
    try:
        # 基础物理化学性质 (15个)
        descriptors.extend([ Descriptors.MolWt(mol), Descriptors.MolLogP(mol), Descriptors.TPSA(mol), Descriptors.NumHDonors(mol), Descriptors.NumHAcceptors(mol), Descriptors.NumRotatableBonds(mol), Descriptors.NumAromaticRings(mol), Descriptors.NumSaturatedRings(mol), Descriptors.NumAliphaticRings(mol), Descriptors.RingCount(mol), Descriptors.FractionCsp3(mol), Descriptors.NumHeteroatoms(mol), Descriptors.HeavyAtomCount(mol), Descriptors.LabuteASA(mol), Descriptors.BertzCT(mol), ])
        # 拓扑描述符 (10个)
        descriptors.extend([ Descriptors.BalabanJ(mol), Descriptors.Chi0(mol), Descriptors.Chi1(mol), Descriptors.Chi0v(mol), Descriptors.Chi1v(mol), Descriptors.HallKierAlpha(mol), Descriptors.Kappa1(mol), Descriptors.Kappa2(mol), Descriptors.Kappa3(mol), Descriptors.Ipc(mol), ])
        # 电子和几何描述符 (10个)
        descriptors.extend([ Descriptors.MaxEStateIndex(mol), Descriptors.MinEStateIndex(mol), Descriptors.MaxAbsEStateIndex(mol), Descriptors.MinAbsEStateIndex(mol), Descriptors.qed(mol), Descriptors.SlogP_VSA1(mol), Descriptors.SlogP_VSA2(mol), Descriptors.SMR_VSA1(mol), Descriptors.SMR_VSA2(mol), Descriptors.PEOE_VSA1(mol), ])
        # 毒性相关描述符 (15个)
        descriptors.extend([ Descriptors.fr_Al_COO(mol), Descriptors.fr_Ar_COO(mol), Descriptors.fr_COO(mol), Descriptors.fr_COO2(mol), Descriptors.fr_C_O(mol), Descriptors.fr_C_O_noCOO(mol), Descriptors.fr_C_S(mol), Descriptors.fr_HOCCN(mol), Descriptors.fr_Imine(mol), Descriptors.fr_NH0(mol), Descriptors.fr_NH1(mol), Descriptors.fr_NH2(mol), Descriptors.fr_N_O(mol), Descriptors.fr_Ndealkylation1(mol), Descriptors.fr_Ndealkylation2(mol), ])
    except: descriptors = [0.0] * 50
    return [0.0 if (np.isnan(x) or np.isinf(x)) else float(x) for x in descriptors]

def get_toxicity_descriptors(mol):
    """计算专门针对毒性预测的15个描述符"""
    descriptors = []
    try: descriptors.extend([ Descriptors.fr_Al_OH(mol), Descriptors.fr_Ar_OH(mol), Descriptors.fr_benzene(mol), Descriptors.fr_furan(mol), Descriptors.fr_imidazole(mol), Descriptors.fr_morpholine(mol), Descriptors.fr_piperdine(mol), Descriptors.fr_piperzine(mol), Descriptors.fr_pyridine(mol), Descriptors.fr_quatN(mol), Descriptors.fr_sulfide(mol), Descriptors.fr_sulfonamd(mol), Descriptors.fr_sulfone(mol), Descriptors.fr_term_acetylene(mol), Descriptors.fr_tetrazole(mol), ])
    except: descriptors = [0.0] * 15
    return descriptors


# ==================== 特征工程核心函数 ====================
def _calculate_single_feature(mol, feature_name, config):
    """内部函数，计算单个分子的单个特征向量"""
    n_bits = config['feature_definitions'][feature_name]['size']
    if feature_name == 'Avalon':
        return list(pyAvalonTools.GetAvalonFP(mol, nBits=n_bits))
    if feature_name == 'MACCS':
        return list(AllChem.GetMACCSKeysFingerprint(mol))
    # Mordred 有单独的批量处理逻辑
    return []

def generate_features(smiles_list, feature_type, config):
    """生成特征的主函数，支持单一特征"""
    print(f"正在生成 {feature_type} 特征...")
    
    # --- 特殊批量处理：Mordred ---
    if feature_type == 'Mordred':
        print("使用 Mordred 批量计算描述符...")
        calc = Calculator(descriptors, ignore_3D=True)
        mols = [Chem.MolFromSmiles(smi) for smi in smiles_list]
        valid_mols_map = {i: mol for i, mol in enumerate(mols) if mol is not None}
        if not valid_mols_map: 
            return np.zeros((len(smiles_list), config['feature_definitions']['Mordred']['size']))
        
        df_mordred = calc.pandas(list(valid_mols_map.values()), nproc=1)
        df_mordred = df_mordred.select_dtypes(include=np.number)
        
        if not hasattr(generate_features, 'mordred_columns'):
            generate_features.mordred_columns = df_mordred.columns.tolist()
        df_mordred = df_mordred.reindex(columns=generate_features.mordred_columns, fill_value=0.0)
        
        final_features = np.zeros((len(smiles_list), len(generate_features.mordred_columns)))
        final_features[list(valid_mols_map.keys())] = df_mordred.values
        final_features = np.nan_to_num(final_features, nan=0.0, posinf=0.0, neginf=0.0)
        
        print(f"生成特征完成，维度: {final_features.shape}")
        return final_features

    # --- 通用循环处理：Avalon和MACCS ---
    if feature_type not in ['Avalon', 'MACCS']:
        raise ValueError(f"不支持的特征类型: {feature_type}. 仅支持: Avalon, MACCS, Mordred")
    
    all_features = []
    for smiles in tqdm(smiles_list, desc=f'生成{feature_type}特征'):
        mol = Chem.MolFromSmiles(smiles)
        if mol:
            features = _calculate_single_feature(mol, feature_type, config)
        else:
            features = [0] * config['feature_definitions'][feature_type]['size']
        all_features.append(features)
    
    features_array = np.array(all_features, dtype=float)
    print(f"生成特征完成，维度: {features_array.shape}")
    return features_array

def preprocess_and_scale_features(X_train, X_test, feature_type, config):
    """
    智能地对特征进行预处理。
    - 纯指纹特征：跳过缩放。
    - 纯描述符特征：进行标准缩放。
    """
    print("\n🔬 开始智能预处理...")
    
    scaler = None
    if feature_type == 'Mordred':
        print(f"✅  检测到单一 Mordred 特征，将对所有 {X_train.shape[1]} 列进行标准化。")
        scaler = StandardScaler()
        X_train_processed = scaler.fit_transform(X_train)
        X_test_processed = scaler.transform(X_test)
        print("标准化完成。")
        return X_train_processed, X_test_processed, scaler
    
    # 如果是指纹特征（Avalon或MACCS），则直接返回
    print("☑️  特征不包含描述符，跳过标准化。")
    return X_train, X_test, scaler


#================== 模型训练与评估函数 ====================
def train_xgboost_with_cv(X_train, y_train, param_grid, cv_folds, random_state, config):
    """使用GridSearchCV进行模型训练和参数搜索"""
    model_name = config['feature_type']
    print(f"\n开始为【{model_name}】特征进行{cv_folds}折交叉验证和参数搜索...")

    # 🔧 修复参数冲突 - 只设置不会与param_grid冲突的基础参数
    xgb_clf = xgb.XGBClassifier(
        objective='binary:logistic',
        random_state=random_state,
        n_jobs=1,
        tree_method='hist',
        verbosity=0
    )
    cv_splitter = StratifiedKFold(n_splits=cv_folds, shuffle=True, random_state=random_state)
    search = GridSearchCV(
        estimator=xgb_clf, param_grid=param_grid, scoring='roc_auc',
        cv=cv_splitter, verbose=1, n_jobs=1, return_train_score=True
    )
    search.fit(X_train, y_train)
    print(f"\n🎉 参数搜索完成！\n最优参数: {search.best_params_}\n最优CV AUC: {search.best_score_:.3f}")
    return search.best_estimator_, search

def perform_detailed_cv_evaluation(X_train, y_train, best_params, cv_folds, random_state):
    """在整个训练集上进行详细的K折交叉验证评估"""
    # 🔧 修复参数冲突问题 - 避免重复传递参数
    model_params = best_params.copy()

    # 设置默认参数，但不覆盖best_params中的设置
    default_params = {
        'objective': 'binary:logistic',
        'random_state': random_state,
        'n_jobs': 1,
        'tree_method': 'hist',
        'verbosity': 0
    }

    # 只添加best_params中没有的参数
    for key, value in default_params.items():
        if key not in model_params:
            model_params[key] = value

    best_model = xgb.XGBClassifier(**model_params)
    cv_splitter = StratifiedKFold(n_splits=cv_folds, shuffle=True, random_state=random_state)
    cv_metrics = {'accuracy': [], 'balanced_accuracy': [], 'precision': [], 'recall': [], 'specificity': [], 'f1': [], 'auc': [], 'aprc': []}
    
    for train_idx, val_idx in cv_splitter.split(X_train, y_train):
        X_fold_train, X_fold_val = X_train[train_idx], X_train[val_idx]
        y_fold_train, y_fold_val = y_train.iloc[train_idx], y_train.iloc[val_idx]
        
        best_model.fit(X_fold_train, y_fold_train)
        y_pred = best_model.predict(X_fold_val)
        y_proba = best_model.predict_proba(X_fold_val)[:, 1]
        
        cm = confusion_matrix(y_fold_val, y_pred)
        # 鲁棒的混淆矩阵解包
        if len(cm.ravel()) == 4:
            tn, fp, fn, tp = cm.ravel()
        else: # 处理只有一类的情况
            tn, fp, fn, tp = 0, 0, 0, 0
            if y_fold_val.nunique() == 1:
                if y_fold_val.iloc[0] == 0: tn = cm[0,0]
                else: tp = cm[0,0]

        cv_metrics['accuracy'].append(accuracy_score(y_fold_val, y_pred))
        cv_metrics['balanced_accuracy'].append(balanced_accuracy_score(y_fold_val, y_pred))
        cv_metrics['precision'].append(precision_score(y_fold_val, y_pred, zero_division=0))
        cv_metrics['recall'].append(recall_score(y_fold_val, y_pred, zero_division=0))
        cv_metrics['specificity'].append(tn / (tn + fp) if (tn + fp) > 0 else 0)
        cv_metrics['f1'].append(f1_score(y_fold_val, y_pred, zero_division=0))
        cv_metrics['auc'].append(roc_auc_score(y_fold_val, y_proba))
        cv_metrics['aprc'].append(average_precision_score(y_fold_val, y_proba))
        
    # 🔧 修复Python版本兼容性问题 - 使用dict.update()替代|操作符
    result = {f'{k}_mean': np.mean(v) for k, v in cv_metrics.items()}
    result.update({f'{k}_std': np.std(v) for k, v in cv_metrics.items()})
    return result

def detailed_evaluation(y_true, y_pred, y_proba):
    """在给定数据集上计算一套完整的评估指标"""
    cm = confusion_matrix(y_true, y_pred)
    tn, fp, _, _ = cm.ravel() if len(cm.ravel()) == 4 else (0,0,0,0)
    return {
        'accuracy': accuracy_score(y_true, y_pred), 
        'balanced_accuracy': balanced_accuracy_score(y_true, y_pred), 
        'precision': precision_score(y_true, y_pred, zero_division=0), 
        'recall': recall_score(y_true, y_pred, zero_division=0), 
        'specificity': tn / (tn + fp) if (tn + fp) > 0 else 0, 
        'f1': f1_score(y_true, y_pred, zero_division=0), 
        'auc': roc_auc_score(y_true, y_proba), 
        'aprc': average_precision_score(y_true, y_proba)
    }

def save_best_model(model, model_info, config):
    """保存最佳模型和相关信息"""
    dataset_name = os.path.splitext(os.path.basename(config['raw_data_path']))[0]
    model_name = f"{dataset_name}_{config['feature_type']}_XGB"
    
    # 确保输出目录存在
    os.makedirs(config['output_dir'], exist_ok=True)
    
    # 保存模型
    model_path = os.path.join(config['output_dir'], f'{model_name}_best_model.joblib')
    joblib.dump(model, model_path)
    
    # 保存模型信息（包括scaler）
    info_path = os.path.join(config['output_dir'], f'{model_name}_model_info.pkl')
    joblib.dump(model_info, info_path)
    
    print(f"✅ 模型已保存:")
    print(f"   模型文件: {model_path}")
    print(f"   信息文件: {info_path}")
    print(f"   是否包含scaler: {'scaler' in model_info}")

# ==================== 主函数 ====================
def main():
    config = CONFIG
    set_random_seeds(config['random_state'])
    print_environment_info()

    model_name = config['feature_type']
    print("=" * 60 + f"\n🚀 开始建模: 【{model_name}】\n" + "=" * 60)
    
    # 1. 读取和清洗数据
    df = pd.read_excel(config['raw_data_path'])
    df = df.dropna(subset=[config['smiles_column'], config['label_column']])
    df = df.drop_duplicates(subset=[config['smiles_column']], keep='first')
    print(f"原始有效数据: {df.shape[0]} 行, 标签分布: {df[config['label_column']].value_counts().to_dict()}")

    # 🔍 数据一致性检查
    print(f"\n📊 数据一致性检查:")
    print(f"  数据文件: {config['raw_data_path']}")
    print(f"  数据形状: {df.shape}")
    print(f"  SMILES列: {config['smiles_column']}")
    print(f"  标签列: {config['label_column']}")
    print(f"  前3个SMILES: {df[config['smiles_column']].head(3).tolist()}")
    print(f"  数据哈希: {hash(tuple(df[config['smiles_column']].tolist()[:10]))}")  # 前10个SMILES的哈希

    X_smiles, y = df[config['smiles_column']], df[config['label_column']]
    
    # 2. 划分训练集和测试集
    X_train_smiles, X_test_smiles, y_train, y_test = train_test_split(
        X_smiles, y, test_size=config['test_size'], random_state=config['random_state'], stratify=y
    )

    # 3. 生成特征
    X_train = generate_features(X_train_smiles.tolist(), model_name, config)
    X_test = generate_features(X_test_smiles.tolist(), model_name, config)

    # 4. 核心修复：使用新的智能预处理函数
    X_train_processed, X_test_processed, scaler = preprocess_and_scale_features(
        X_train, X_test, model_name, config
    )
        
    # 5. 模型训练与评估
    best_model, search_results = train_xgboost_with_cv(
        X_train_processed, y_train, config['param_grid'],
        config['cv_folds'], config['random_state'], config
    )

    # 6. 详细交叉验证性能分析
    print("\n📊 正在进行详细的交叉验证性能评估...")
    cv_stats = perform_detailed_cv_evaluation(X_train_processed, y_train, search_results.best_params_, config['cv_folds'], config['random_state'])

    # 7. 最终模型评估
    print("📈 正在评估最终模型在训练集和测试集上的性能...")
    y_train_pred = best_model.predict(X_train_processed)
    y_train_proba = best_model.predict_proba(X_train_processed)[:, 1]
    train_metrics = detailed_evaluation(y_train, y_train_pred, y_train_proba)

    y_test_pred = best_model.predict(X_test_processed)
    y_test_proba = best_model.predict_proba(X_test_processed)[:, 1]
    test_metrics = detailed_evaluation(y_test, y_test_pred, y_test_proba)

    # 8. 综合性能总结
    print("\n" + "=" * 90 + f"\n🎉【{model_name}】特征建模完成 - 综合性能报告\n" + "=" * 90)
    print(f"{'指标':<18} {'训练集':<12} {'十折交叉验证':<20} {'测试集':<12}")
    print("-" * 85)
    metrics_display = [
        ('准确率', 'accuracy'), ('平衡准确率', 'balanced_accuracy'), ('精确率', 'precision'), 
        ('召回率', 'recall'), ('特异性', 'specificity'), ('F1分数', 'f1'), 
        ('ROC AUC', 'auc'), ('PR AUC', 'aprc')
    ]
    for display_name, metric_key in metrics_display:
        train_val = train_metrics[metric_key]
        cv_mean = cv_stats[f'{metric_key}_mean']
        cv_std = cv_stats[f'{metric_key}_std']
        test_val = test_metrics[metric_key]
        print(f"{display_name:<18} {train_val:<12.3f} {f'{cv_mean:.3f}±{cv_std:.3f}':<19} {test_val:<12.3f}")
    
    test_auc = test_metrics['auc']
    performance_level = "🌟 优秀" if test_auc >= 0.9 else "✅ 良好" if test_auc >= 0.8 else "⚠️ 一般" if test_auc >= 0.7 else "❌ 较差"
    print("\n" + "=" * 90 + f"\n   模型性能等级: {performance_level} (测试集 ROC AUC: {test_auc:.3f})\n" + "=" * 90)

    # 9. 保存最佳模型
    # 创建模型信息字典
    model_info = {
        'dataset_name': os.path.splitext(os.path.basename(config['raw_data_path']))[0],
        'feature_type': config['feature_type'],
        'feature_definitions': config['feature_definitions'],
        'random_state': config['random_state'],
        'data_path': config['raw_data_path'],
        'train_metrics': train_metrics,
        'test_metrics': test_metrics,
        'cv_stats': cv_stats,
        'best_params': best_model.get_params(),
        'scaler': scaler  # 关键：添加scaler
    }
    
    save_best_model(best_model, model_info, config)

    # 10. 保存完整数据集的预测结果
    print("\n📊 正在生成完整数据集的预测结果...")
    
    # 创建数据集划分标记
    df_with_set = df.copy()
    df_with_set['set'] = 'unknown'  # 默认值
    
    # 标记训练集和测试集
    train_indices = X_train_smiles.index
    test_indices = X_test_smiles.index
    df_with_set.loc[train_indices, 'set'] = 'train'
    df_with_set.loc[test_indices, 'set'] = 'test'
    
    # 对完整数据集生成特征并预测
    X_all = generate_features(df[config['smiles_column']].tolist(), model_name, config)
    X_all_processed, _, _ = preprocess_and_scale_features(X_all, X_all, model_name, config)
    
    # 生成预测结果
    y_all_pred = best_model.predict(X_all_processed)
    y_all_proba = best_model.predict_proba(X_all_processed)
    
    # 添加预测结果到数据框
    df_with_set['prediction'] = y_all_pred
    df_with_set['probability_class_0'] = y_all_proba[:, 0]
    df_with_set['probability_class_1'] = y_all_proba[:, 1]
    
    # 保存预测结果
    dataset_name = os.path.splitext(os.path.basename(config['raw_data_path']))[0]
    prediction_filename = f"{dataset_name}_{config['feature_type']}_XGB_predictions.xlsx"
    prediction_path = os.path.join(config['output_dir'], prediction_filename)
    
    df_with_set.to_excel(prediction_path, index=False)
    print(f"✅ 完整预测结果已保存至: {prediction_path}")
    print(f"   包含 {len(df_with_set)} 个样本的预测结果和数据集划分信息")

if __name__ == '__main__':
    main()


